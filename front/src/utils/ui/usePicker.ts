import { ref, computed } from 'vue'

type PickerOption = {
  text: string
  value: any
}

type UsePickerOptions = {
  initialValue?: any
  options: PickerOption[]
}

export const usePicker = ({ initialValue, options }: UsePickerOptions) => {
  const pickerValue = ref(initialValue)
  const pickerOptions = ref(options)

  const selectedIndex = computed(() => {
    const index = pickerOptions.value.findIndex(opt => opt.value === pickerValue.value)
    return index === -1 ? 0 : index
  })

  const selectedText = computed(() => {
    const selectedOption = pickerOptions.value[selectedIndex.value]
    return selectedOption ? selectedOption.text : ''
  })

  const onPickerChange = (event: any) => {
    const index = event.detail.value
    pickerValue.value = pickerOptions.value[index].value
  }

  return {
    pickerValue,
    pickerOptions,
    selectedIndex,
    selectedText,
    onPickerChange,
  }
}