# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a local lifestyle O2O service platform targeting tier-3/4 cities and blue-collar workers. The project consists of a frontend uni-app cross-platform application and a Go backend service.

### Core Features
- **Job Recruitment** - Full-time, part-time, and internship job postings
- **Gig Economy** - Temporary work marketplace with order management
- **Real Estate** - Rental, second-hand, and new property listings
- **Dating/Social** - Local community features and user matching
- **Instant Messaging** - Real-time chat system

## Project Structure

### 根目录结构
```
fnbdb-mini/
├── CLAUDE.md           # 项目开发指南和架构说明
├── DESIGN.md          # 设计文档
├── backend/           # Go后端服务
└── front/             # Vue3 + uni-app前端应用
```

### 前端结构 (front/)
```
front/
├── README.md          # 前端项目说明
├── package.json       # 依赖管理和脚本配置
├── pnpm-lock.yaml     # 锁定依赖版本
├── tsconfig.json      # TypeScript配置
├── vite.config.ts     # Vite构建配置
├── uno.config.ts      # UnoCSS原子化CSS配置
├── manifest.json      # uni-app应用配置清单
├── pages.json         # 页面路由配置
├── index.html         # H5入口文件
├── env/               # 环境变量配置
├── dist/              # 构建输出目录
├── node_modules/      # 依赖包
├── static/            # 静态资源
│   ├── images/        # 图片资源
│   ├── tabbar/        # 底部导航图标
│   └── svg/           # SVG矢量图标
└── src/               # 源代码目录
    ├── App.vue        # 应用根组件
    ├── main.ts        # 应用入口文件
    ├── pages.json     # 页面配置
    ├── manifest.json  # 应用配置
    ├── api/           # API接口定义
    │   ├── index.ts   # API统一导出
    │   ├── auth.ts    # 认证相关API
    │   ├── user.ts    # 用户相关API
    │   └── gig.ts     # 零工相关API
    ├── components/    # 可复用组件
    │   ├── common/    # 通用组件
    │   ├── gig/       # 零工模块组件
    │   ├── job/       # 招聘模块组件
    │   ├── house/     # 房产模块组件
    │   ├── dating/    # 交友模块组件
    │   ├── home/      # 首页模块组件
    │   ├── post/      # 发布模块组件
    │   └── thorui/    # ThorUI组件库
    ├── pages/         # 页面组件
    │   ├── index/     # 首页
    │   ├── auth/      # 认证页面
    │   ├── home/      # 主页面
    │   ├── gig/       # 零工页面
    │   ├── job/       # 招聘页面
    │   ├── house/     # 房产页面
    │   ├── dating/    # 交友页面
    │   ├── message/   # 消息页面
    │   ├── mine/      # 个人中心
    │   └── post/      # 发布页面
    ├── stores/        # Pinia状态管理
    │   ├── index.ts   # Store统一导出
    │   ├── global.ts  # 全局状态
    │   ├── user.ts    # 用户状态
    │   └── job.ts     # 招聘状态
    ├── constants/     # 常量定义
    │   ├── index.ts   # 常量统一导出
    │   ├── common.ts  # 通用常量
    │   ├── gig.ts     # 零工常量
    │   ├── house.ts   # 房产常量
    │   ├── response.ts # 响应码常量
    │   ├── standards.ts # 标准数据常量
    │   └── static/    # 静态数据常量
    ├── utils/         # 工具函数
    │   ├── index.ts   # 工具统一导出
    │   ├── core/      # 核心工具函数
    │   │   ├── date.ts      # 日期处理
    │   │   ├── format.ts    # 格式化工具
    │   │   ├── validation.ts # 验证工具
    │   │   └── string.ts    # 字符串工具
    │   ├── business/  # 业务工具函数
    │   │   └── gig.ts       # 零工业务工具
    │   ├── network/   # 网络请求工具
    │   │   ├── client.ts    # HTTP客户端
    │   │   ├── request.ts   # 请求封装
    │   │   └── helpers.ts   # 请求辅助
    │   └── ui/        # UI相关工具
    │       ├── feedback.ts  # 反馈提示
    │       └── navigation.ts # 导航工具
    ├── services/      # 业务服务层
    │   ├── index.ts   # 服务统一导出
    │   ├── base.ts    # 基础服务
    │   ├── auth.ts    # 认证服务
    │   ├── gig.ts     # 零工服务
    │   ├── house.ts   # 房产服务
    │   ├── dating.ts  # 交友服务
    │   ├── messaging.ts # 消息服务
    │   └── upload.ts  # 上传服务
    ├── types/         # TypeScript类型定义
    │   ├── common.ts  # 通用类型
    │   ├── user.ts    # 用户类型
    │   ├── gig.ts     # 零工类型
    │   ├── house.ts   # 房产类型
    │   ├── auth.ts    # 认证类型
    │   └── services.ts # 服务类型
    └── styles/        # 样式文件
        ├── app.css    # 全局样式
```

### 后端结构 (backend/)
```
backend/
├── Makefile           # 构建和开发命令
├── README.md          # 后端项目说明
├── go.mod             # Go模块依赖管理
├── go.sum             # 依赖版本锁定
├── bin/               # 编译后的二进制文件
├── tmp/               # 热重载临时文件
├── logs/              # 应用日志文件
├── cmd/               # 应用入口点
│   └── server/
│       └── main.go    # 主服务入口
├── configs/           # 配置文件
│   ├── config.yaml    # 默认配置
│   ├── config.dev.yaml # 开发环境配置
│   └── config.prod.yaml # 生产环境配置
├── deployments/       # 部署相关文件
│   ├── Dockerfile     # Docker镜像构建
│   └── docker-compose.yml # Docker编排
├── migrations/        # 数据库迁移文件
│   ├── schema.sql     # 数据库架构
│   ├── test_users.sql # 测试用户数据
│   └── job_module_*.sql # 招聘模块迁移
├── scripts/           # 脚本文件
│   └── install-tools.sh # 开发工具安装
├── design/            # 设计文档
│   ├── chat_system_design.md # 聊天系统设计
│   ├── verification_service_architecture.md # 认证服务架构
│   └── *.md           # 其他设计文档
├── docs/              # API文档
│   ├── api_design_and_naming_convention.md # API设计规范
│   ├── gig/           # 零工模块文档
│   └── job/           # 招聘模块文档
├── internal/          # 内部应用代码
│   ├── api/           # API层
│   │   ├── apiproto/  # gRPC协议定义
│   │   ├── controller/ # 控制器层
│   │   ├── middleware/ # 中间件
│   │   ├── router/    # 路由配置
│   │   ├── server.go  # 服务器配置
│   │   └── wire.go    # 依赖注入配置
│   ├── constants/     # 常量定义
│   │   ├── business.go # 业务常量
│   │   ├── gig.go     # 零工常量
│   │   ├── job.go     # 招聘常量
│   │   ├── standards.go # 标准常量
│   │   └── status.go  # 状态常量
│   ├── model/         # 数据模型
│   │   ├── model.go   # 基础模型
│   │   ├── user.go    # 用户模型
│   │   ├── gig.go     # 零工模型
│   │   ├── job.go     # 招聘模型
│   │   ├── enterprise.go # 企业模型
│   │   ├── chat.go    # 聊天模型
│   │   └── *.go       # 其他业务模型
│   ├── repository/    # 数据访问层
│   │   ├── user_repo.go # 用户仓库
│   │   ├── gig_repo.go # 零工仓库
│   │   ├── job_repo.go # 招聘仓库
│   │   └── *.go       # 其他仓库实现
│   ├── service/       # 业务逻辑层
│   │   ├── auth_svc.go # 认证服务
│   │   ├── user_svc.go # 用户服务
│   │   ├── gig_svc.go # 零工服务
│   │   ├── job_svc.go # 招聘服务
│   │   └── *.go       # 其他业务服务
│   ├── types/         # 类型定义
│   │   ├── common_types.go # 通用类型
│   │   ├── auth_types.go # 认证类型
│   │   ├── gig_types.go # 零工类型
│   │   ├── job_types.go # 招聘类型
│   │   └── *.go       # 其他业务类型
│   ├── utils/         # 工具函数
│   │   ├── crypto.go  # 加密工具
│   │   ├── http.go    # HTTP工具
│   │   ├── string.go  # 字符串工具
│   │   ├── time.go    # 时间工具
│   │   └── uid.go     # ID生成工具
│   ├── scheduler/     # 定时任务
│   │   ├── scheduler.go # 调度器
│   │   ├── gig_scheduler.go # 零工定时任务
│   │   └── job.go     # 招聘定时任务
│   └── worker/        # 后台工作器
│       ├── gig_worker.go # 零工工作器
│       └── wire.go    # 依赖注入配置
├── pkg/               # 可重用的库代码
│   ├── cache/         # 缓存封装
│   │   ├── cache.go   # 缓存接口
│   │   └── redis.go   # Redis实现
│   ├── config/        # 配置管理
│   │   └── config.go  # 配置加载
│   ├── database/      # 数据库封装
│   │   └── postgres.go # PostgreSQL连接
│   ├── logger/        # 日志封装
│   │   └── logger.go  # 日志配置
│   ├── jwt/           # JWT工具
│   │   └── jwt.go     # JWT生成验证
│   ├── response/      # 响应封装
│   │   ├── response.go # 统一响应格式
│   │   └── constants.go # 响应常量
│   ├── sms/           # 短信服务
│   │   ├── sms.go     # 短信接口
│   │   └── aliyun.go  # 阿里云短信
│   ├── storage/       # 存储服务
│   │   ├── storage.go # 存储接口
│   │   └── qiniu.go   # 七牛云存储
│   ├── wechat/        # 微信服务
│   │   ├── client.go  # 微信客户端
│   │   ├── auth.go    # 微信认证
│   │   ├── payment.go # 微信支付
│   │   └── types.go   # 微信类型
│   ├── centrifugo/    # 实时通信
│   │   └── client.go  # Centrifugo客户端
│   └── validator/     # 数据验证
│       └── validator.go # 验证器配置
├── test/              # 测试文件
└── wechat/            # 微信相关配置
```

### 目录职责说明

#### 前端目录职责

- **src/api/**: API接口层，与后端服务通信，使用alova进行请求管理，支持useRequest和callApi两种模式
- **src/components/**: 可复用组件，按业务模块分组，遵循组件化设计原则
- **src/pages/**: 页面级组件，对应路由页面，实现具体业务功能
- **src/stores/**: Pinia状态管理，管理全局状态和业务状态
- **src/constants/**: 常量定义，包含业务常量、状态码、静态数据等
- **src/utils/**: 工具函数，分为核心工具、业务工具、网络工具、UI工具
- **src/services/**: 业务服务层，封装复杂业务逻辑，主要使用alova的useRequest模式处理列表、详情等API调用
- **src/types/**: TypeScript类型定义，确保类型安全
- **src/styles/**: 样式文件，主要使用styles/app.css全局样式，配合UnoCSS原子化CSS进行样式管理

#### 后端目录职责

- **cmd/**: 应用程序入口点，包含main函数和启动逻辑
- **internal/api/**: API层，包含控制器、中间件、路由配置
- **internal/service/**: 业务逻辑层，实现核心业务功能
- **internal/repository/**: 数据访问层，封装数据库操作
- **internal/model/**: 数据模型层，定义数据结构和关系
- **internal/types/**: 类型定义，包含请求响应结构体
- **internal/constants/**: 常量定义，业务常量和状态码
- **internal/utils/**: 内部工具函数，仅供内部使用
- **pkg/**: 可重用库代码，可被外部项目引用
- **migrations/**: 数据库迁移文件，管理数据库结构变更
- **configs/**: 配置文件，支持多环境配置
- **deployments/**: 部署相关文件，Docker和容器编排
- **docs/**: 项目文档，API文档和设计文档

### 架构设计规范

#### 前端架构规范
1. **组件设计**: 遵循单一职责原则，组件功能明确
2. **状态管理**: 使用Pinia进行全局状态管理，支持持久化
3. **API管理**: 使用alova统一管理API请求，支持缓存和重试。分为两种模式：useRequest（列表、详情等）和callApi（提交操作，支持全屏加载）
4. **类型安全**: 使用TypeScript确保类型安全，定义完整的类型系统
5. **样式管理**: 使用UnoCSS原子化CSS（在uno.config.ts配置预设）+ styles/app.css全局样式，布局优先使用UnoCSS配置好的原子化类名
6. **工具函数**: 分层设计，核心工具和业务工具分离

#### 后端架构规范
1. **分层架构**: Controller → Service → Repository → Model 清晰分层
2. **依赖注入**: 使用Google Wire进行依赖管理
3. **接口设计**: 每层定义清晰的接口，便于测试和扩展
4. **错误处理**: 统一错误处理机制，标准化错误响应
5. **配置管理**: 使用Viper进行配置管理，支持多环境
6. **数据库设计**: 使用GORM ORM，支持软删除和审计字段

### 开发规范

#### 命名规范
- **前端**: 文件使用kebab-case，组件使用PascalCase，函数使用camelCase
- **后端**: 文件使用snake_case，结构体使用PascalCase，函数使用camelCase
- **数据库**: 表名使用snake_case，字段名使用snake_case

#### 代码组织
- **功能模块化**: 按业务模块组织代码，每个模块独立开发
- **接口优先**: 先定义接口，再实现具体逻辑
- **测试驱动**: 编写单元测试和集成测试
- **文档同步**: 代码变更同步更新文档


### Database Design Specifications
- Utilize Postgresql data support types, appropriate field types and index types to enhance query efficiency and performance
- Include audit fields (`created_at`, `updated_at`) if using soft deletion (`is_del` field)
- Prohibit the use of foreign key constraints, ENUM, etc. in tables as they can affect performance or be difficult to maintain
- Use the string type for status fields and apply constraints using CHECK, for example: ``` status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft','recruiting','paused','locked','in_progress','completed','closed')), ```
  Except for fields such as education, gender, and is_del which are globally used and have fixed statuses, smallint types can be used
- In SQL for data tables, try to set NOT NULL and design DEFAULT values
- For fields related to transactions, orders, and product prices in the table, use int types for storage, with units of cents; except for salaries in recruitment ranging from 8000 to 10000, which can use the yuan unit as it does not involve transactions, payments, and bills


## Development Commands

### Frontend (from front/ directory)
```bash
# Development
pnpm dev:mp-weixin    # WeChat Mini Program
pnpm dev:h5           # H5 Web App
pnpm dev:mp-alipay    # Alipay Mini Program

# Build
pnpm build:mp-weixin  # Build WeChat Mini Program
pnpm build:h5         # Build H5 Web App

# Type checking
pnpm type-check       # TypeScript type checking
```

### Backend (from backend/ directory)
```bash
# Development
make dev              # Start with hot reload (using air)
make run              # Start without hot reload

# Build
make build            # Build binary to bin/server

# Code quality
make check            # Basic checks (fmt, imports, vet, tidy)
make check-all        # Complete checks (includes staticcheck, sec, errcheck)

# Testing
make test             # Run all tests
make test-coverage    # Generate test coverage report

# Tools
make install-tools    # Install development tools
```

## Architecture Patterns

### Frontend Architecture
- **Component-based**: Modular Vue 3 components with TypeScript
- **State Management**: Pinia stores for global state
- **API Layer**: Centralized API definitions using alova，支持useRequest和callApi两种请求模式
- **Utility Functions**: Shared utilities for common operations

### Backend Architecture
- **Layered Architecture**: Controller → Service → Repository → Model
- **Dependency Injection**: Google Wire for dependency management
- **Middleware Pattern**: Unified request processing pipeline
- **Error Handling**: Standardized error response format

### Constants Use
Constants are defined in the "constants" directory. Please try to use the predefined constants in your business logic and follow this practice. This is more convenient for maintenance and ensures standardization.

### Alova Request Patterns

#### useRequest模式 (用于Services层)
主要用于列表、详情等数据获取场景，提供响应式数据和状态管理：

```typescript
// services层使用useRequest
import { useRequest } from 'alova/client'
import { gigApi } from '@/api/gig'

export const useGigList = () => {
  const {
    data: gigList,
    loading,
    error,
    send
  } = useRequest(gigApi.getGigList(), {
    immediate: true, // 立即请求
    initialData: [] // 初始数据
  })
  
  return {
    gigList,
    loading,
    error,
    refresh: send
  }
}
```

#### callApi模式 (用于提交等操作)
主要用于表单提交、数据更新等操作，内置loading管理和错误处理：

```typescript
// 页面组件中使用callApi - 基础用法（自动错误提示）
import { callApi } from '@/utils/network/helpers'
import { gigApi } from '@/api/gig'

const handleSubmit = async () => {
  const result = await callApi(() => gigApi.createGig(formData))
  if (result.success) {
    // 处理成功逻辑
    uni.navigateBack()
  }
}

// 高级用法 - 自定义loading和成功提示
const handleSubmitWithOptions = async () => {
  const result = await callApi(() => gigApi.createGig(formData), {
    showLoading: true,
    loadingText: '发布中...',
    showSuccessToast: true,
    successText: '零工发布成功',
    onSuccess: () => {
      // 成功回调
      uni.navigateBack()
    }
  })
}

// 使用快捷函数 - submitForm
import { submitForm } from '@/utils/network/helpers'

const handleQuickSubmit = async () => {
  await submitForm(() => gigApi.createGig(formData), {
    loadingText: '发布中...',
    successText: '零工发布成功',
    onSuccess: () => uni.navigateBack()
  })
}
```

## Technology Stack

### Frontend
- **Framework**: Vue 3 + TypeScript + uni-app CLI
- **State Management**: Pinia
- **UI Libraries**: uni-ui, uv-ui, ThorUI
- **Network**: alova for API requests (工作流化简化的新一代请求工具，文档：https://alova.js.org/zh-CN/)
- **Styling**: UnoCSS (原子化CSS) + styles/app.css (全局样式)
- **Constants**: 在src/constants目录中定义了全局和相关模块的常量
- **Icon**: 优先使用@iconify-json/solar风格图标, 其次使用@iconify-json/carbon风格图标

### Backend
- **Language**: Go 1.24.0
- **Web Framework**: Gin
- **Database**: PostgreSQL + GORM
- **Cache**: Redis
- **Real-time**: Centrifugo
- **Authentication**: JWT
- **Configuration**: Viper
- **Constants**: 在backend/internal/constants目录中定义了全局和相关模块的常量

### Icon
- iconify-json/solar网址:https://icones.js.org/collection/solar

## Key Development Guidelines

### API Design
- Use RESTful principles with snake_case for JSON keys
- Frontend uses camelCase, backend uses snake_case
- All APIs versioned with `/api/v1/` prefix
- Consistent error response format

### Code Quality
- **Backend**: Run `make check` before committing
- **Frontend**: Run `pnpm type-check` to validate TypeScript
- Follow established patterns in existing code
- Use dependency injection for backend services

### Alova Usage Guidelines
- **useRequest使用场景**: 
  - 列表数据获取（支持分页、搜索、筛选）
  - 详情数据获取（单个实体信息）
  - 需要响应式数据和状态管理的场景
  - 在services层封装，供组件使用
  
- **callApi使用场景**:
  - 表单提交（创建、更新、删除操作）
  - 不需要响应式数据的操作
  - 内置loading管理，支持自动显示/隐藏加载状态
  - 内置错误处理，自动显示错误提示
  - 支持重试机制和超时控制
  - 提供多种快捷函数：submitForm、deleteItem、silentCall等
  - 直接在页面组件中使用


## Common Patterns

### Frontend Component Structure
```typescript
// components follow this pattern
interface Props {
  title: string
  count?: number
}

const props = withDefaults(defineProps<Props>(), {
  count: 0
})

const emit = defineEmits<{
  click: [value: string]
}>()
```


### Backend Service Pattern
```go
// services follow this pattern
type ServiceInterface interface {
  GetByID(ctx context.Context, id uint) (*Model, error)
  Create(ctx context.Context, req *CreateRequest) error
}

type service struct {
  repo RepositoryInterface
}
```

## Testing Strategy

### Frontend
- Component testing with Vue Test Utils
- API mocking for unit tests
- E2E testing for critical user flows

### Backend
- Unit tests for service layer
- Integration tests for API endpoints
- Table-driven tests for business logic

## Development Standards and Anti-Patterns

### Core Principles
- **Simplicity First**: This is a small-to-medium WeChat mini-program project. Avoid over-engineering and complex abstractions.
- **Direct Implementation**: Prefer straightforward solutions over multiple layers of abstraction.
- **Minimal Encapsulation**: Only encapsulate when there's clear, immediate benefit.

### Strict Anti-Patterns to AVOID

#### Function Duplication
- ❌ **NEVER** create multiple functions that do essentially the same thing with minor variations
- ❌ **NEVER** create "wrapper" functions that just call other functions without adding value
- ❌ **AVOID** names like `formatDateSimple`, `formatDateTime`, `formatDateTimeChinese` for the same core functionality
- ✅ **USE** enum-based parameters to handle variations: `formatDate(date, DateFormat.CHINESE)`

#### Over-Encapsulation
- ❌ **NEVER** create business utility functions that just call core utils without adding logic
- ❌ **AVOID** functions like `formatGigDuration()` that just call `formatWorkDuration()`
- ❌ **AVOID** creating "helper" functions for single-use scenarios
- ✅ **USE** core utilities directly in business code when appropriate

#### Constants vs Logic Confusion
- ❌ **NEVER** put logic functions in constants files
- ❌ **AVOID** functions like `getStatusDetails()` in constants files
- ✅ **CONSTANTS files**: Only data, enums, and mappings
- ✅ **BUSINESS utils**: Functions that operate on data

#### Compatibility Code Bloat
- ❌ **NEVER** add "compatibility" exports unless there's active legacy code
- ❌ **AVOID** comments like "for compatibility with existing code" without proof
- ❌ **REMOVE** unused compatibility functions immediately
- ✅ **REFACTOR** calling code to use new functions directly

### Mandatory Development Rules

1. **One Function, One Purpose**: If you can't explain a function's unique value in one sentence, it shouldn't exist.

2. **Direct Usage**: Business code should use core utilities directly when no business logic is added.

3. **Function Naming**: Names must clearly indicate what makes each function unique.

4. **Constants Purity**: Constants files contain only data structures, no functions.

5. **Type Compatibility**: Use appropriate types for the mini-program environment (number for setTimeout, not NodeJS.Timeout).

6. **Enum Over Functions**: Use enums with a single function instead of multiple similar functions.

### Code Review Checklist
Before any utility function addition, ask:
- [ ] Does this function add unique business logic?
- [ ] Is there an existing function that does 80%+ of this?
- [ ] Can this be solved with parameters instead of new functions?
- [ ] Will this function be used in more than one place?
- [ ] Does the name clearly indicate its unique purpose?

## Important Notes

- **Working Directory**: Always ensure you're in the correct subdirectory (front/ or backend/)
- **Dependencies**: Frontend and backend manage dependencies independently
- **Configuration**: Environment-specific config files for different deployment stages
- **Hot Reload**: Use `make dev` for backend and `pnpm dev:mp-weixin` for frontend development

## Architecture Improvements (2024)

### Frontend Utils Refactoring
The frontend utility layer was refactored to eliminate redundancy and over-engineering:

#### Date/Time Functions Consolidation
- **Before**: Multiple similar functions (`formatDateSimple`, `formatDateTime`, `formatDateChinese`, etc.)
- **After**: Single `formatDate()` function with `DateFormat` enum parameter
- **Unified relative time**: `formatRelativeTime()` with `RelativeTimeMode` parameter
- **Simplified duration**: Direct calculation without wrapper functions

#### Business Layer Cleanup
- **Removed**: Redundant wrapper functions in `business/gig.ts`
- **Pattern**: Business utils should add logic, not just call core functions
- **Migration**: Logic functions moved from `constants/` to appropriate `utils/business/` files

#### Type System Improvements
- **Fixed**: NodeJS type compatibility issues for mini-program environment
- **Standardized**: Use `number` for setTimeout return values instead of `NodeJS.Timeout`

#### Function Naming Standards
- **Specific naming**: `formatGigSalary` instead of generic `formatSalary`
- **Clear responsibility**: Each function has a unique, well-defined purpose
- **No duplicates**: Eliminated functions that do essentially the same thing

### Architecture Principles Applied
1. **Simplicity over Complexity**: Removed unnecessary abstractions
2. **Direct Usage**: Core utilities used directly when no business logic is added
3. **Enum-based Variation**: Parameters instead of multiple similar functions
4. **Type Safety**: Proper types for the target environment
5. **Clear Separation**: Constants contain only data, not logic functions

These changes significantly reduced maintenance overhead while maintaining all functionality. The codebase is now more maintainable and has clearer responsibility boundaries.

## Migration Notes

### For Future Development
When adding new utility functions, always ask:
- Does this add unique business logic or just call another function?
- Can this be solved with parameters instead of new functions?
- Is there already a function that does 90%+ of this?
- Will this function be used in multiple places?

### Function Usage Examples
```typescript
// ✅ Correct - using unified date formatting
import { formatDate, DateFormat } from '@/utils/core/date'
formatDate(date, DateFormat.CHINESE)  // Instead of formatDateChinese()

// ✅ Correct - using business function with logic
import { formatGigSalary } from '@/utils/business/gig'
formatGigSalary(salary, unit, true)  // Adds business logic for salary display

// ✅ Correct - direct usage when no business logic needed
import { formatTimeRange } from '@/utils/core/date'
formatTimeRange(start, end)  // Direct usage, no wrapper needed
```