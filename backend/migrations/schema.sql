-- ====================================================================
-- 数据库表结构定义
-- 创建时间: 2025-07-12
-- 描述: 项目所有表的建表SQL
-- ====================================================================
-- 启用 PostGIS 扩展
CREATE EXTENSION IF NOT EXISTS postgis;
-- 1. 用户主表
CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    uid VARCHAR(64) NOT NULL DEFAULT '',
    -- 用户唯一标识码
    openid VARCHAR(128) NOT NULL DEFAULT '',
    -- 微信OpenID
    unionid VARCHAR(128) DEFAULT '',
    -- 微信UnionID
    phone VARCHAR(20) NOT NULL DEFAULT '',
    -- 手机号码
    nickname VARCHAR(50) DEFAULT '',
    -- 用户昵称
    avatar VARCHAR(255) DEFAULT '',
    -- 头像URL
    gender SMALLINT DEFAULT 0,
    -- 性别: 0-未知, 1-男, 2-女
    status SMALLINT DEFAULT 1,
    -- 用户状态: 1-正常, 0-禁用
    birthday DATE DEFAULT NULL,
    -- 生日
    is_verified BOOLEAN DEFAULT FALSE,
    -- 是否已认证
    personal_verification_id BIGINT DEFAULT 0,
    -- 个人认证记录ID
    enterprise_id BIGINT DEFAULT 0,
    -- 关联企业ID, 0表示未认证企业
    points INTEGER DEFAULT 0,
    -- 积分余额
    phone_changed_at TIMESTAMP(0) DEFAULT NULL,
    -- 手机号最后修改时间
    source VARCHAR(20) NOT NULL DEFAULT 'mp-weixin',
    -- 注册来源: mp-weixin-微信小程序, sms-短信
    settings JSONB DEFAULT '{}',
    -- 用户设置JSON
    last_login_at TIMESTAMP(0) DEFAULT NULL,
    -- 最后登录时间
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    -- 创建时间
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP -- 更新时间
);
-- 用户表索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_users_openid ON users (openid);
CREATE UNIQUE INDEX IF NOT EXISTS uk_users_phone ON users (phone);
CREATE INDEX IF NOT EXISTS idx_users_status ON users (status);
-- CREATE INDEX IF NOT EXISTS idx_users_enterprise_id ON users (enterprise_id);
-- 用户表字段注释
COMMENT ON COLUMN users.uid IS '用户唯一标识码';
COMMENT ON COLUMN users.openid IS '微信OpenID';
COMMENT ON COLUMN users.unionid IS '微信UnionID';
COMMENT ON COLUMN users.phone IS '手机号码';
COMMENT ON COLUMN users.nickname IS '用户昵称';
COMMENT ON COLUMN users.avatar IS '头像URL';
COMMENT ON COLUMN users.gender IS '性别: 0-未知, 1-男, 2-女';
COMMENT ON COLUMN users.status IS '用户状态: 1-正常, 0-禁用';
COMMENT ON COLUMN users.birthday IS '生日';
COMMENT ON COLUMN users.is_verified IS '是否已认证';
COMMENT ON COLUMN users.personal_verification_id IS '个人认证记录ID';
COMMENT ON COLUMN users.enterprise_id IS '关联企业ID, 0表示未认证企业';
COMMENT ON COLUMN users.points IS '积分余额';
COMMENT ON COLUMN users.phone_changed_at IS '手机号最后修改时间';
COMMENT ON COLUMN users.source IS '注册来源: mp-weixin-微信小程序, sms-短信';
COMMENT ON COLUMN users.settings IS '用户设置JSON';
COMMENT ON COLUMN users.last_login_at IS '最后登录时间';
COMMENT ON COLUMN users.created_at IS '创建时间';
COMMENT ON COLUMN users.updated_at IS '更新时间';
-- ====================================================================
-- 2. 用户资料详情表
CREATE TABLE IF NOT EXISTS user_profiles (
    user_id BIGINT PRIMARY KEY,
    height INTEGER DEFAULT 0,
    -- 身高(cm)
    weight INTEGER DEFAULT 0,
    -- 体重(kg)
    education INTEGER DEFAULT 0,
    -- 学历等级: 1-小学, 2-初中, 3-高中, 4-大专, 5-本科, 6-硕士, 7-博士
    occupation VARCHAR(100) DEFAULT '',
    -- 职业
    income INTEGER DEFAULT 0,
    -- 月收入(元)
    hobbies TEXT DEFAULT '',
    -- 兴趣爱好
    self_intro TEXT DEFAULT '',
    -- 个人简介
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    -- 创建时间
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP -- 更新时间
);
-- 用户资料详情表字段注释
COMMENT ON COLUMN user_profiles.user_id IS '用户ID';
COMMENT ON COLUMN user_profiles.height IS '身高(cm)';
COMMENT ON COLUMN user_profiles.weight IS '体重(kg)';
COMMENT ON COLUMN user_profiles.education IS '学历等级: 1-小学, 2-初中, 3-高中, 4-大专, 5-本科, 6-硕士, 7-博士';
COMMENT ON COLUMN user_profiles.occupation IS '职业';
COMMENT ON COLUMN user_profiles.income IS '月收入(元)';
COMMENT ON COLUMN user_profiles.hobbies IS '兴趣爱好';
COMMENT ON COLUMN user_profiles.self_intro IS '个人简介';
COMMENT ON COLUMN user_profiles.created_at IS '创建时间';
COMMENT ON COLUMN user_profiles.updated_at IS '更新时间';
-- ====================================================================
-- 3. 用户设置表
CREATE TABLE IF NOT EXISTS user_settings (
    user_id BIGINT PRIMARY KEY,
    allow_message_from INTEGER DEFAULT 1,
    -- 允许消息来源: 1-所有人, 2-关注的人, 3-不允许
    allow_location_share BOOLEAN DEFAULT TRUE,
    -- 是否允许位置共享
    allow_job_recommend BOOLEAN DEFAULT TRUE,
    -- 是否允许工作推荐
    allow_house_recommend BOOLEAN DEFAULT TRUE,
    -- 是否允许房源推荐
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    -- 创建时间
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP -- 更新时间
);
-- 用户设置表字段注释
COMMENT ON COLUMN user_settings.user_id IS '用户ID';
COMMENT ON COLUMN user_settings.allow_message_from IS '允许消息来源: 1-所有人, 2-关注的人, 3-不允许';
COMMENT ON COLUMN user_settings.allow_location_share IS '是否允许位置共享';
COMMENT ON COLUMN user_settings.allow_job_recommend IS '是否允许工作推荐';
COMMENT ON COLUMN user_settings.allow_house_recommend IS '是否允许房源推荐';
COMMENT ON COLUMN user_settings.created_at IS '创建时间';
COMMENT ON COLUMN user_settings.updated_at IS '更新时间';
-- ====================================================================
-- 4. 个人认证表
CREATE TABLE IF NOT EXISTS personal_verifications (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL DEFAULT 0,
    -- 用户ID
    real_name VARCHAR(50) DEFAULT '',
    -- 真实姓名(掩码)
    real_name_encrypted TEXT DEFAULT '',
    -- 真实姓名(加密)
    id_card VARCHAR(50) DEFAULT '',
    -- 身份证号(掩码)
    id_card_encrypted TEXT DEFAULT '',
    -- 身份证号(加密)
    real_name_hash VARCHAR(64) DEFAULT '',
    -- 姓名哈希值
    id_card_hash VARCHAR(64) DEFAULT '',
    -- 身份证哈希值
    id_card_front_image VARCHAR(255) DEFAULT '',
    -- 身份证正面照片
    id_card_back_image VARCHAR(255) DEFAULT '',
    -- 身份证背面照片
    hand_held_image VARCHAR(255) DEFAULT '',
    -- 手持身份证照片
    status INTEGER DEFAULT 0,
    -- 认证状态: 0-待审核, 1-通过, 2-拒绝
    reject_reason TEXT DEFAULT '',
    -- 拒绝原因
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    -- 创建时间
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP -- 更新时间
);
-- 个人认证表索引
CREATE INDEX IF NOT EXISTS idx_personal_verifications_user_id ON personal_verifications (user_id);
CREATE INDEX IF NOT EXISTS idx_personal_verifications_status ON personal_verifications (status);
CREATE INDEX IF NOT EXISTS idx_personal_verifications_id_card_hash ON personal_verifications (id_card_hash);
-- 个人认证表字段注释
COMMENT ON COLUMN personal_verifications.id IS '主键ID';
COMMENT ON COLUMN personal_verifications.user_id IS '用户ID';
COMMENT ON COLUMN personal_verifications.real_name IS '真实姓名(掩码)';
COMMENT ON COLUMN personal_verifications.real_name_encrypted IS '真实姓名(加密)';
COMMENT ON COLUMN personal_verifications.id_card IS '身份证号(掩码)';
COMMENT ON COLUMN personal_verifications.id_card_encrypted IS '身份证号(加密)';
COMMENT ON COLUMN personal_verifications.real_name_hash IS '姓名哈希值';
COMMENT ON COLUMN personal_verifications.id_card_hash IS '身份证哈希值';
COMMENT ON COLUMN personal_verifications.id_card_front_image IS '身份证正面照片';
COMMENT ON COLUMN personal_verifications.id_card_back_image IS '身份证背面照片';
COMMENT ON COLUMN personal_verifications.hand_held_image IS '手持身份证照片';
COMMENT ON COLUMN personal_verifications.status IS '认证状态: 0-待审核, 1-通过, 2-拒绝';
COMMENT ON COLUMN personal_verifications.reject_reason IS '拒绝原因';
COMMENT ON COLUMN personal_verifications.created_at IS '创建时间';
COMMENT ON COLUMN personal_verifications.updated_at IS '更新时间';
-- ====================================================================
-- 5. 企业认证表
CREATE TABLE IF NOT EXISTS enterprise_verifications (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL DEFAULT 0,
    -- 用户ID
    enterprise_name VARCHAR(100) DEFAULT '',
    -- 企业名称
    unified_social_credit_code VARCHAR(50) DEFAULT '',
    -- 统一社会信用代码(掩码)
    unified_social_credit_code_encrypted TEXT DEFAULT '',
    -- 统一社会信用代码(加密)
    legal_representative VARCHAR(50) DEFAULT '',
    -- 法定代表人(掩码)
    legal_representative_encrypted TEXT DEFAULT '',
    -- 法定代表人(加密)
    legal_representative_id_card VARCHAR(50) DEFAULT '',
    -- 法人身份证(掩码)
    legal_representative_id_card_encrypted TEXT DEFAULT '',
    -- 法人身份证(加密)
    enterprise_name_hash VARCHAR(64) DEFAULT '',
    -- 企业名称哈希值
    credit_code_hash VARCHAR(64) DEFAULT '',
    -- 信用代码哈希值
    legal_representative_hash VARCHAR(64) DEFAULT '',
    -- 法人哈希值
    business_license_image VARCHAR(255) DEFAULT '',
    -- 营业执照照片
    legal_representative_id_card_front_image VARCHAR(255) DEFAULT '',
    -- 法人身份证正面
    legal_representative_id_card_back_image VARCHAR(255) DEFAULT '',
    -- 法人身份证背面
    authorization_letter VARCHAR(255) DEFAULT '',
    -- 授权委托书
    status INTEGER DEFAULT 0,
    -- 认证状态: 0-待审核, 1-通过, 2-拒绝
    reject_reason TEXT DEFAULT '',
    -- 拒绝原因
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    -- 创建时间
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP -- 更新时间
);
-- 企业认证表索引
CREATE INDEX IF NOT EXISTS idx_enterprise_verifications_user_id ON enterprise_verifications (user_id);
CREATE INDEX IF NOT EXISTS idx_enterprise_verifications_status ON enterprise_verifications (status);
CREATE INDEX IF NOT EXISTS idx_enterprise_verifications_credit_code_hash ON enterprise_verifications (credit_code_hash);
-- 企业认证表字段注释
COMMENT ON COLUMN enterprise_verifications.id IS '主键ID';
COMMENT ON COLUMN enterprise_verifications.user_id IS '用户ID';
COMMENT ON COLUMN enterprise_verifications.enterprise_name IS '企业名称';
COMMENT ON COLUMN enterprise_verifications.unified_social_credit_code IS '统一社会信用代码(掩码)';
COMMENT ON COLUMN enterprise_verifications.unified_social_credit_code_encrypted IS '统一社会信用代码(加密)';
COMMENT ON COLUMN enterprise_verifications.legal_representative IS '法定代表人(掩码)';
COMMENT ON COLUMN enterprise_verifications.legal_representative_encrypted IS '法定代表人(加密)';
COMMENT ON COLUMN enterprise_verifications.legal_representative_id_card IS '法人身份证(掩码)';
COMMENT ON COLUMN enterprise_verifications.legal_representative_id_card_encrypted IS '法人身份证(加密)';
COMMENT ON COLUMN enterprise_verifications.enterprise_name_hash IS '企业名称哈希值';
COMMENT ON COLUMN enterprise_verifications.credit_code_hash IS '信用代码哈希值';
COMMENT ON COLUMN enterprise_verifications.legal_representative_hash IS '法人哈希值';
COMMENT ON COLUMN enterprise_verifications.business_license_image IS '营业执照照片';
COMMENT ON COLUMN enterprise_verifications.legal_representative_id_card_front_image IS '法人身份证正面';
COMMENT ON COLUMN enterprise_verifications.legal_representative_id_card_back_image IS '法人身份证背面';
COMMENT ON COLUMN enterprise_verifications.authorization_letter IS '授权委托书';
COMMENT ON COLUMN enterprise_verifications.status IS '认证状态: 0-待审核, 1-通过, 2-拒绝';
COMMENT ON COLUMN enterprise_verifications.reject_reason IS '拒绝原因';
COMMENT ON COLUMN enterprise_verifications.created_at IS '创建时间';
COMMENT ON COLUMN enterprise_verifications.updated_at IS '更新时间';
-- ====================================================================
-- 6. 企业表
CREATE TABLE IF NOT EXISTS enterprises (
    id BIGSERIAL PRIMARY KEY,
    enterprise_id VARCHAR(64) NOT NULL DEFAULT '',
    -- 企业唯一标识
    user_id BIGINT NOT NULL DEFAULT 0,
    -- 创建用户ID
    enterprise_name VARCHAR(100) DEFAULT '',
    -- 企业名称
    enterprise_type VARCHAR(50) DEFAULT '',
    -- 企业类型
    unified_social_credit_code VARCHAR(50) DEFAULT '',
    -- 统一社会信用代码
    legal_representative VARCHAR(50) DEFAULT '',
    -- 法定代表人
    registered_address TEXT DEFAULT '',
    -- 注册地址
    contact_phone VARCHAR(20) DEFAULT '',
    -- 联系电话
    contact_email VARCHAR(100) DEFAULT '',
    -- 联系邮箱
    business_scope TEXT DEFAULT '',
    -- 经营范围
    establishment_date DATE DEFAULT NULL,
    -- 成立日期
    employee_count INTEGER DEFAULT 0,
    -- 员工数量
    registered_capital DECIMAL(15, 2) DEFAULT 0.00,
    -- 注册资本
    status INTEGER DEFAULT 1,
    -- 企业状态: 1-正常, 0-停用
    verification_status VARCHAR(20) DEFAULT 'pending',
    -- 认证状态: pending-待认证, approved-已认证, rejected-已拒绝
    verification_id BIGINT DEFAULT 0,
    -- 认证记录ID
    member_level INTEGER DEFAULT 0,
    -- 会员等级: 0-普通, 1-VIP, 2-SVIP
    member_expire_at TIMESTAMP(0) DEFAULT NULL,
    -- 会员到期时间
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    -- 创建时间
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP -- 更新时间
);
-- 企业表索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_enterprises_enterprise_id ON enterprises (enterprise_id);
CREATE INDEX IF NOT EXISTS idx_enterprises_user_id ON enterprises (user_id);
CREATE INDEX IF NOT EXISTS idx_enterprises_status ON enterprises (status);
CREATE INDEX IF NOT EXISTS idx_enterprises_verification_status ON enterprises (verification_status);
-- 企业表字段注释
COMMENT ON COLUMN enterprises.id IS '主键ID';
COMMENT ON COLUMN enterprises.enterprise_id IS '企业唯一标识';
COMMENT ON COLUMN enterprises.user_id IS '创建用户ID';
COMMENT ON COLUMN enterprises.enterprise_name IS '企业名称';
COMMENT ON COLUMN enterprises.enterprise_type IS '企业类型';
COMMENT ON COLUMN enterprises.unified_social_credit_code IS '统一社会信用代码';
COMMENT ON COLUMN enterprises.legal_representative IS '法定代表人';
COMMENT ON COLUMN enterprises.registered_address IS '注册地址';
COMMENT ON COLUMN enterprises.contact_phone IS '联系电话';
COMMENT ON COLUMN enterprises.contact_email IS '联系邮箱';
COMMENT ON COLUMN enterprises.business_scope IS '经营范围';
COMMENT ON COLUMN enterprises.establishment_date IS '成立日期';
COMMENT ON COLUMN enterprises.employee_count IS '员工数量';
COMMENT ON COLUMN enterprises.registered_capital IS '注册资本';
COMMENT ON COLUMN enterprises.status IS '企业状态: 1-正常, 0-停用';
COMMENT ON COLUMN enterprises.verification_status IS '认证状态: pending-待认证, approved-已认证, rejected-已拒绝';
COMMENT ON COLUMN enterprises.verification_id IS '认证记录ID';
COMMENT ON COLUMN enterprises.member_level IS '会员等级: 0-普通, 1-VIP, 2-SVIP';
COMMENT ON COLUMN enterprises.member_expire_at IS '会员到期时间';
COMMENT ON COLUMN enterprises.created_at IS '创建时间';
COMMENT ON COLUMN enterprises.updated_at IS '更新时间';
-- ====================================================================
-- 7. 手机号更换记录表
CREATE TABLE IF NOT EXISTS phone_change_records (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL DEFAULT 0,
    -- 用户ID
    old_phone VARCHAR(20) DEFAULT '',
    -- 原手机号
    new_phone VARCHAR(20) DEFAULT '',
    -- 新手机号
    change_type VARCHAR(20) DEFAULT '',
    -- 更换方式: sms-短信, wechat-微信
    status INTEGER DEFAULT 0,
    -- 更换状态: 0-失败, 1-成功
    fail_reason TEXT DEFAULT '',
    -- 失败原因
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    -- 创建时间
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP -- 更新时间
);
-- 手机号更换记录表索引
CREATE INDEX IF NOT EXISTS idx_phone_change_records_user_id ON phone_change_records (user_id);
CREATE INDEX IF NOT EXISTS idx_phone_change_records_created_at ON phone_change_records (created_at);
-- 手机号更换记录表字段注释
COMMENT ON COLUMN phone_change_records.id IS '主键ID';
COMMENT ON COLUMN phone_change_records.user_id IS '用户ID';
COMMENT ON COLUMN phone_change_records.old_phone IS '原手机号';
COMMENT ON COLUMN phone_change_records.new_phone IS '新手机号';
COMMENT ON COLUMN phone_change_records.change_type IS '更换方式: sms-短信, wechat-微信';
COMMENT ON COLUMN phone_change_records.status IS '更换状态: 0-失败, 1-成功';
COMMENT ON COLUMN phone_change_records.fail_reason IS '失败原因';
COMMENT ON COLUMN phone_change_records.created_at IS '创建时间';
COMMENT ON COLUMN phone_change_records.updated_at IS '更新时间';
-- ====================================================================
-- 8. 会话表
CREATE TABLE IF NOT EXISTS conversations (
    id BIGSERIAL PRIMARY KEY,
    conversation_id VARCHAR(64) NOT NULL DEFAULT '',
    -- 会话唯一标识
    conversation_type INTEGER DEFAULT 1,
    -- 会话类型: 1-单聊, 2-群聊, 3-系统通知
    creator_id BIGINT DEFAULT 0,
    -- 创建者用户ID
    title VARCHAR(100) DEFAULT '',
    -- 会话标题
    avatar VARCHAR(255) DEFAULT '',
    -- 会话头像
    description TEXT DEFAULT '',
    -- 会话描述
    member_count INTEGER DEFAULT 0,
    -- 成员数量
    last_message_id BIGINT DEFAULT 0,
    -- 最后一条消息ID
    last_message_time TIMESTAMP(0) DEFAULT NULL,
    -- 最后消息时间
    is_active BOOLEAN DEFAULT TRUE,
    -- 是否活跃
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    -- 创建时间
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP -- 更新时间
);
-- 会话表索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_conversations_conversation_id ON conversations (conversation_id);
CREATE INDEX IF NOT EXISTS idx_conversations_type ON conversations (conversation_type);
CREATE INDEX IF NOT EXISTS idx_conversations_creator_id ON conversations (creator_id);
CREATE INDEX IF NOT EXISTS idx_conversations_is_active ON conversations (is_active);
-- 会话表字段注释
COMMENT ON COLUMN conversations.id IS '主键ID';
COMMENT ON COLUMN conversations.conversation_id IS '会话唯一标识';
COMMENT ON COLUMN conversations.conversation_type IS '会话类型: 1-单聊, 2-群聊, 3-系统通知';
COMMENT ON COLUMN conversations.creator_id IS '创建者用户ID';
COMMENT ON COLUMN conversations.title IS '会话标题';
COMMENT ON COLUMN conversations.avatar IS '会话头像';
COMMENT ON COLUMN conversations.description IS '会话描述';
COMMENT ON COLUMN conversations.member_count IS '成员数量';
COMMENT ON COLUMN conversations.last_message_id IS '最后一条消息ID';
COMMENT ON COLUMN conversations.last_message_time IS '最后消息时间';
COMMENT ON COLUMN conversations.is_active IS '是否活跃';
COMMENT ON COLUMN conversations.created_at IS '创建时间';
COMMENT ON COLUMN conversations.updated_at IS '更新时间';
-- ====================================================================
-- 9. 用户会话关系表
CREATE TABLE IF NOT EXISTS user_conversations (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL DEFAULT 0,
    -- 用户ID
    conversation_id BIGINT NOT NULL DEFAULT 0,
    -- 会话ID
    is_visible BOOLEAN DEFAULT TRUE,
    -- 是否可见
    is_muted BOOLEAN DEFAULT FALSE,
    -- 是否静音
    is_pinned BOOLEAN DEFAULT FALSE,
    -- 是否置顶
    unread_count INTEGER DEFAULT 0,
    -- 未读消息数
    last_read_message_id BIGINT DEFAULT 0,
    -- 最后已读消息ID
    last_active_time TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    -- 最后活跃时间
    joined_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    -- 加入时间
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    -- 创建时间
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP -- 更新时间
);
-- 用户会话关系表索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_user_conversations_user_conversation ON user_conversations (user_id, conversation_id);
CREATE INDEX IF NOT EXISTS idx_user_conversations_user_id ON user_conversations (user_id);
CREATE INDEX IF NOT EXISTS idx_user_conversations_conversation_id ON user_conversations (conversation_id);
-- 用户会话关系表字段注释
COMMENT ON COLUMN user_conversations.id IS '主键ID';
COMMENT ON COLUMN user_conversations.user_id IS '用户ID';
COMMENT ON COLUMN user_conversations.conversation_id IS '会话ID';
COMMENT ON COLUMN user_conversations.is_visible IS '是否可见';
COMMENT ON COLUMN user_conversations.is_muted IS '是否静音';
COMMENT ON COLUMN user_conversations.is_pinned IS '是否置顶';
COMMENT ON COLUMN user_conversations.unread_count IS '未读消息数';
COMMENT ON COLUMN user_conversations.last_read_message_id IS '最后已读消息ID';
COMMENT ON COLUMN user_conversations.last_active_time IS '最后活跃时间';
COMMENT ON COLUMN user_conversations.joined_at IS '加入时间';
COMMENT ON COLUMN user_conversations.created_at IS '创建时间';
COMMENT ON COLUMN user_conversations.updated_at IS '更新时间';
-- ====================================================================
-- 10. 消息表
CREATE TABLE IF NOT EXISTS messages (
    id BIGSERIAL PRIMARY KEY,
    msg_id VARCHAR(64) NOT NULL DEFAULT '',
    -- 消息唯一标识
    conversation_id BIGINT NOT NULL DEFAULT 0,
    -- 会话ID
    sender_id BIGINT NOT NULL DEFAULT 0,
    -- 发送者用户ID
    content TEXT DEFAULT '',
    -- 消息内容
    message_type INTEGER DEFAULT 1,
    -- 消息类型: 1-文本, 2-图片, 3-语音, 4-视频, 5-文件, 6-位置, 7-系统
    status INTEGER DEFAULT 1,
    -- 消息状态: 1-已发送, 2-已送达, 3-已读, 4-已撤回, 5-发送失败
    extra JSONB DEFAULT '{}',
    -- 扩展信息JSON
    reply_to_message_id BIGINT DEFAULT 0,
    -- 回复的消息ID
    is_revoked BOOLEAN DEFAULT FALSE,
    -- 是否已撤回
    revoked_at TIMESTAMP(0) DEFAULT NULL,
    -- 撤回时间
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    -- 创建时间
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP -- 更新时间
);
-- 消息表索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_messages_msg_id ON messages (msg_id);
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages (conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages (sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages (created_at);
CREATE INDEX IF NOT EXISTS idx_messages_status ON messages (status);
-- 消息表字段注释
COMMENT ON COLUMN messages.id IS '主键ID';
COMMENT ON COLUMN messages.msg_id IS '消息唯一标识';
COMMENT ON COLUMN messages.conversation_id IS '会话ID';
COMMENT ON COLUMN messages.sender_id IS '发送者用户ID';
COMMENT ON COLUMN messages.content IS '消息内容';
COMMENT ON COLUMN messages.message_type IS '消息类型: 1-文本, 2-图片, 3-语音, 4-视频, 5-文件, 6-位置, 7-系统';
COMMENT ON COLUMN messages.status IS '消息状态: 1-已发送, 2-已送达, 3-已读, 4-已撤回, 5-发送失败';
COMMENT ON COLUMN messages.extra IS '扩展信息JSON';
COMMENT ON COLUMN messages.reply_to_message_id IS '回复的消息ID';
COMMENT ON COLUMN messages.is_revoked IS '是否已撤回';
COMMENT ON COLUMN messages.revoked_at IS '撤回时间';
COMMENT ON COLUMN messages.created_at IS '创建时间';
COMMENT ON COLUMN messages.updated_at IS '更新时间';
-- ====================================================================
-- 11. 系统通知表
CREATE TABLE IF NOT EXISTS notifications (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL DEFAULT 0,
    -- 接收用户ID
    notification_type VARCHAR(50) DEFAULT '',
    -- 通知类型
    level VARCHAR(20) DEFAULT 'info',
    -- 通知级别: info-信息, warning-警告, error-错误
    status VARCHAR(20) DEFAULT 'unread',
    -- 通知状态: unread-未读, read-已读
    title VARCHAR(100) DEFAULT '',
    -- 通知标题
    content TEXT DEFAULT '',
    -- 通知内容
    summary VARCHAR(255) DEFAULT '',
    -- 通知摘要
    data JSONB DEFAULT '{}',
    -- 通知数据JSON
    action_url VARCHAR(255) DEFAULT '',
    -- 操作链接
    action_text VARCHAR(50) DEFAULT '',
    -- 操作按钮文本
    image_url VARCHAR(255) DEFAULT '',
    -- 通知图片
    icon_url VARCHAR(255) DEFAULT '',
    -- 通知图标
    read_at TIMESTAMP(0) DEFAULT NULL,
    -- 已读时间
    expire_at TIMESTAMP(0) DEFAULT NULL,
    -- 过期时间
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    -- 创建时间
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP -- 更新时间
);
-- 系统通知表索引
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications (user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications (notification_type);
CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications (status);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications (created_at);
-- 系统通知表字段注释
COMMENT ON COLUMN notifications.id IS '主键ID';
COMMENT ON COLUMN notifications.user_id IS '接收用户ID';
COMMENT ON COLUMN notifications.notification_type IS '通知类型';
COMMENT ON COLUMN notifications.level IS '通知级别: info-信息, warning-警告, error-错误';
COMMENT ON COLUMN notifications.status IS '通知状态: unread-未读, read-已读';
COMMENT ON COLUMN notifications.title IS '通知标题';
COMMENT ON COLUMN notifications.content IS '通知内容';
COMMENT ON COLUMN notifications.summary IS '通知摘要';
COMMENT ON COLUMN notifications.data IS '通知数据JSON';
COMMENT ON COLUMN notifications.action_url IS '操作链接';
COMMENT ON COLUMN notifications.action_text IS '操作按钮文本';
COMMENT ON COLUMN notifications.image_url IS '通知图片';
COMMENT ON COLUMN notifications.icon_url IS '通知图标';
COMMENT ON COLUMN notifications.read_at IS '已读时间';
COMMENT ON COLUMN notifications.expire_at IS '过期时间';
COMMENT ON COLUMN notifications.created_at IS '创建时间';
COMMENT ON COLUMN notifications.updated_at IS '更新时间';
-- 1. 零工主表
CREATE TABLE IF NOT EXISTS gigs (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL DEFAULT 0,
    title VARCHAR(100) NOT NULL DEFAULT '',
    description TEXT NOT NULL DEFAULT '',
    -- 薪酬信息(重要字段放在前面)
    salary INTEGER NOT NULL DEFAULT 0,
    -- 薪酬(分)
    salary_unit SMALLINT DEFAULT 1 NOT NULL,
    -- 薪酬单位: 1-小时, 2-天, 3-件, 4-总价
    settlement SMALLINT DEFAULT 1 NOT NULL,
    -- 结算方式: 1-日结, 2-周结, 3-月结, 4-完工结
    -- 人数管理
    people_count SMALLINT NOT NULL,
    -- 招聘人数
    current_people_count SMALLINT DEFAULT 0,
    -- 当前报名人数
    -- 时间控制
    start_time TIMESTAMP(0) NOT NULL,
    -- 开始时间
    end_time TIMESTAMP(0) NOT NULL,
    -- 结束时间
    work_duration INTEGER NOT NULL DEFAULT 0,
    -- 工作时长(分钟，后端计算生成)
    expire_time TIMESTAMP(0) NOT NULL,
    -- 自动关闭时间(end_time + 10min)
    -- 地址信息
    address_name VARCHAR(255) DEFAULT '',
    -- 位置简称(uni.chooseLocation的res.name)
    address VARCHAR(255) NOT NULL DEFAULT '',
    -- 具体地址(uni.chooseLocation的res.address)
    detail_address VARCHAR(255) DEFAULT '',
    -- 详细地址(用户输入的楼层门牌号等)
    full_address VARCHAR(500) NOT NULL DEFAULT '',
    -- 完整地址(address + detail_address)
    location geography(Point, 4326),
    -- 工作地点PostGIS坐标点
    latitude DECIMAL(10, 7) DEFAULT 0,
    -- 纬度
    longitude DECIMAL(10, 7) DEFAULT 0,
    -- 经度
    -- 招聘要求
    gender SMALLINT DEFAULT 0 NOT NULL,
    -- 性别要求: 0-不限, 1-男, 2-女
    age_min SMALLINT DEFAULT 18 NOT NULL,
    -- 最小年龄
    age_max SMALLINT DEFAULT 60 NOT NULL,
    -- 最大年龄
    experience SMALLINT DEFAULT 0 NOT NULL,
    -- 经验要求: 0-不限, 1-应届生, ...
    education SMALLINT DEFAULT 0 NOT NULL,
    -- 学历要求: 0-不限, 1-初中, ...
    skills TEXT DEFAULT '' NOT NULL,
    contact_name VARCHAR(50) NOT NULL,
    contact_phone VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (
        status IN (
            'draft',
            'recruiting',
            'paused',
            'locked',
            'in_progress',
            'completed',
            'closed'
        )
    ),
    -- 状态
    approval_mode VARCHAR(20) DEFAULT 'manual' NOT NULL,
    -- 审批模式: manual-手动, auto-自动录用
    check_in_method VARCHAR(20) DEFAULT 'none' NOT NULL,
    -- 打卡方式: none-无, gps-GPS, qrcode-二维码
    close_reason VARCHAR(100) DEFAULT '',
    -- 关闭原因: expired, manual, filled
    priority SMALLINT DEFAULT 0,
    -- 优先级
    is_urgent BOOLEAN DEFAULT FALSE,
    -- 是否紧急
    -- 关联信息
    -- 发布者ID
    company_name VARCHAR(100) DEFAULT '',
    -- 公司名称
    tags JSONB DEFAULT '[]',
    -- 标签(JSON数组)
    images JSONB DEFAULT '[]',
    -- 图片(JSON数组)
    -- 基础字段
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    is_del SMALLINT NOT NULL DEFAULT 0 -- 软删除: 0-未删除, >0-已删除
);
-- 创建索引
CREATE INDEX IF NOT EXISTS idx_gigs_user_id ON gigs (user_id)
WHERE is_del = 0;
CREATE INDEX IF NOT EXISTS idx_gigs_status ON gigs (is_del, status, created_at)
WHERE is_del = 0;
-- 为 location 创建 GIST 空间索引
CREATE INDEX IF NOT EXISTS idx_gigs_location ON gigs USING GIST (location);
-- 字段注释
COMMENT ON COLUMN gigs.title IS '工作标题';
COMMENT ON COLUMN gigs.description IS '工作描述';
COMMENT ON COLUMN gigs.work_duration IS '工作时长(分钟，后端计算生成)';
COMMENT ON COLUMN gigs.location IS '工作地点PostGIS坐标点';
COMMENT ON COLUMN gigs.address IS '地址';
COMMENT ON COLUMN gigs.detail_address IS '详细地址(楼号楼层等)';
COMMENT ON COLUMN gigs.full_address IS '完整地址';
COMMENT ON COLUMN gigs.latitude IS '纬度';
COMMENT ON COLUMN gigs.longitude IS '经度';
COMMENT ON COLUMN gigs.salary IS '薪酬(分)';
COMMENT ON COLUMN gigs.salary_unit IS '薪酬单位: 1-小时, 2-天, 3-件, 4-总价';
COMMENT ON COLUMN gigs.people_count IS '招聘人数';
COMMENT ON COLUMN gigs.settlement IS '结算方式: 1-日结, 2-周结, 3-月结, 4-完工结';
COMMENT ON COLUMN gigs.gender IS '性别要求: 0-不限, 1-男, 2-女';
COMMENT ON COLUMN gigs.age_min IS '最小年龄';
COMMENT ON COLUMN gigs.age_max IS '最大年龄';
COMMENT ON COLUMN gigs.experience IS '经验要求: 0-不限, 1-应届生, ...';
COMMENT ON COLUMN gigs.education IS '学历要求: 0-不限, 1-初中, ...';
COMMENT ON COLUMN gigs.skills IS '技能要求';
COMMENT ON COLUMN gigs.contact_name IS '联系人姓名';
COMMENT ON COLUMN gigs.contact_phone IS '联系电话';
COMMENT ON COLUMN gigs.status IS '状态: 状态：draft = 草稿, recruiting = 招募中, paused = 暂停中, locked = 招募截止, in_progress = 进行中, completed = 已完成, closed = 已关闭';
COMMENT ON COLUMN gigs.close_reason IS '关闭原因: expired, manual, filled';
COMMENT ON COLUMN gigs.priority IS '优先级';
COMMENT ON COLUMN gigs.is_urgent IS '是否紧急';
COMMENT ON COLUMN gigs.start_time IS '开始时间';
COMMENT ON COLUMN gigs.end_time IS '结束时间';
COMMENT ON COLUMN gigs.expire_time IS '自动关闭时间(end_time + 10min)';
COMMENT ON COLUMN gigs.user_id IS '发布者ID';
COMMENT ON COLUMN gigs.company_name IS '公司名称';
COMMENT ON COLUMN gigs.tags IS '标签(JSON数组)';
COMMENT ON COLUMN gigs.images IS '图片(JSON数组)';
COMMENT ON COLUMN gigs.created_at IS '创建时间';
COMMENT ON COLUMN gigs.updated_at IS '更新时间';
COMMENT ON COLUMN gigs.is_del IS '软删除: 0-未删除, >0-已删除';
-- ====================================================================
-- 2. 零工申请表
CREATE TABLE IF NOT EXISTS gig_applications (
    id BIGSERIAL PRIMARY KEY,
    gig_id BIGINT NOT NULL DEFAULT 0,
    user_id BIGINT NOT NULL DEFAULT 0,
    status VARCHAR(20) DEFAULT 'pending',
    -- 状态: pending/approved/rejected/withdrawn/confirmed/cancelled/completed
    message VARCHAR(500) NOT NULL DEFAULT '',
    -- 申请留言
    -- 申请者基本信息
    applicant_name VARCHAR(50) DEFAULT '',
    -- 申请者姓名
    applicant_phone VARCHAR(20) DEFAULT '',
    -- 申请者联系电话
    has_experience BOOLEAN DEFAULT FALSE,
    -- 是否有相关经验
    experience_description VARCHAR(255) DEFAULT '',
    -- 经验简述
    -- 审核信息
    reviewed_at TIMESTAMP(0) DEFAULT NULL,
    -- 审核时间
    reason VARCHAR(255) DEFAULT '',
    -- 拒绝原因/审核备注
    -- 打卡信息
    check_in_at TIMESTAMP(0) DEFAULT NULL,
    check_out_at TIMESTAMP(0) DEFAULT NULL,
    -- 基础字段
    created_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP,
    is_del SMALLINT NOT NULL DEFAULT 0,
    -- 软删除: 0-未删除, >0-已删除
    -- 唯一约束：同一用户对同一零工只能申请一次
    CONSTRAINT uk_gig_applications_gig_user UNIQUE (gig_id, user_id, is_del)
);
-- 创建索引
CREATE INDEX IF NOT EXISTS idx_gig_applications_gig_id ON gig_applications (gig_id);
CREATE INDEX IF NOT EXISTS idx_gig_applications_user_id ON gig_applications (user_id);
CREATE INDEX IF NOT EXISTS idx_gig_applications_is_del_status ON gig_applications (is_del, status, created_at);
-- 字段注释
COMMENT ON COLUMN gig_applications.gig_id IS '零工ID';
COMMENT ON COLUMN gig_applications.user_id IS '申请者ID';
COMMENT ON COLUMN gig_applications.status IS '状态: pending/approved/rejected/withdrawn/confirmed/cancelled/completed';
COMMENT ON COLUMN gig_applications.message IS '申请留言';
COMMENT ON COLUMN gig_applications.applicant_name IS '申请者姓名';
COMMENT ON COLUMN gig_applications.applicant_phone IS '申请者联系电话';
COMMENT ON COLUMN gig_applications.has_experience IS '是否有相关经验';
COMMENT ON COLUMN gig_applications.experience_description IS '经验简述';
COMMENT ON COLUMN gig_applications.reviewed_at IS '审核时间';
COMMENT ON COLUMN gig_applications.reason IS '拒绝原因/审核备注';
COMMENT ON COLUMN gig_applications.is_del IS '软删除: 0-未删除, >0-已删除';
-- ====================================================================